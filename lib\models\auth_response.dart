import 'package:json_annotation/json_annotation.dart';
import 'package:loan_pawn_flutter/models/user.dart';

part 'auth_response.g.dart';

@JsonSerializable()
class Token {
  @JsonKey(name: 'access_token')
  final String accessToken;
  
  @Json<PERSON>ey(name: 'refresh_token')
  final String refreshToken;
  
  @Json<PERSON>ey(name: 'token_type')
  final String tokenType;
  
  @Json<PERSON>ey(name: 'expires_in')
  final int expiresIn;

  const Token({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
  });

  factory Token.fromJson(Map<String, dynamic> json) => _$TokenFromJson(json);
  Map<String, dynamic> toJson() => _$TokenToJson(this);
}

@JsonSerializable()
class LoginResponse {
  final User user;
  final Token token;
  final String message;
  
  @JsonKey(name: 'message_khmer')
  final String messageKhmer;

  const LoginResponse({
    required this.user,
    required this.token,
    required this.message,
    required this.messageKhmer,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

@JsonSerializable()
class LogoutResponse {
  final String message;
  
  @JsonKey(name: 'message_khmer')
  final String messageKhmer;

  const LogoutResponse({
    required this.message,
    required this.messageKhmer,
  });

  factory LogoutResponse.fromJson(Map<String, dynamic> json) => _$LogoutResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LogoutResponseToJson(this);
}
