// TODO: Implement ApiService as per project requirements
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:loan_pawn_flutter/models/loan_application.dart';
import 'package:loan_pawn_flutter/models/auth_response.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: "https://lc-groupscop.up.railway.app")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // --- Auth ---
  @POST("/api/v1/auth/login")
  @FormUrlEncoded()
  Future<LoginResponse> login(
      @Field("username") String username, @Field("password") String password);

  @POST("/api/v1/auth/login/email")
  Future<LoginResponse> loginWithEmail(
      @Body() Map<String, dynamic> credentials);

  @POST("/api/v1/auth/logout")
  Future<LogoutResponse> logout();

  // --- Loan Applications ---
  @GET("/api/v1/loans/")
  Future<List<LoanApplication>> getLoanApplications();

  @GET("/api/v1/loans/{id}")
  Future<LoanApplication> getLoanApplication(@Path("id") String id);

  @POST("/api/v1/loans/")
  Future<LoanApplication> createApplication(@Body() LoanApplication application);

  @PUT("/api/v1/loans/{id}")
  Future<LoanApplication> updateLoanApplication(@Path("id") String id, @Body() LoanApplication application);

  @DELETE("/applications/{id}")
  Future<void> deleteLoanApplication(@Path("id") String id);
}
