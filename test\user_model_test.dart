import 'package:flutter_test/flutter_test.dart';
import 'package:loan_pawn_flutter/models/user.dart';

void main() {
  group('User Model Tests', () {
    test('Should parse user with integer ID correctly', () {
      // Simulate backend response with integer ID
      final json = {
        'id': 123, // Integer ID from backend
        'fullName': '<PERSON>',
        'email': '<EMAIL>',
        'role': 'po',
        'phoneNumber': '+1234567890'
      };

      final user = User.fromJson(json);

      expect(user.id, '123'); // Should be converted to string
      expect(user.fullName, '<PERSON>');
      expect(user.email, '<EMAIL>');
      expect(user.role, UserRole.pawnOfficer);
      expect(user.phoneNumber, '+1234567890');
    });

    test('Should parse user with string ID correctly', () {
      // Simulate backend response with string ID
      final json = {
        'id': '456', // String ID from backend
        'fullName': '<PERSON>',
        'email': '<EMAIL>',
        'role': 'cpo',
        'phoneNumber': null
      };

      final user = User.from<PERSON><PERSON>(json);

      expect(user.id, '456'); // Should remain as string
      expect(user.fullName, '<PERSON>');
      expect(user.email, '<EMAIL>');
      expect(user.role, UserRole.chiefPawnOfficer);
      expect(user.phoneNumber, null);
    });

    test('Should handle unknown role correctly', () {
      final json = {
        'id': 789,
        'fullName': 'Bob Wilson',
        'email': '<EMAIL>',
        'role': 'unknown_role', // Unknown role
        'phoneNumber': null
      };

      final user = User.fromJson(json);

      expect(user.id, '789');
      expect(user.role, UserRole.unknown);
    });

    test('Should handle null role correctly', () {
      final json = {
        'id': 101,
        'fullName': 'Alice Brown',
        'email': '<EMAIL>',
        'role': null, // Null role
        'phoneNumber': null
      };

      final user = User.fromJson(json);

      expect(user.id, '101');
      expect(user.role, UserRole.unknown);
    });

    test('Should handle all role types correctly', () {
      final roles = {
        'po': UserRole.pawnOfficer,
        'cpo': UserRole.chiefPawnOfficer,
        'teller': UserRole.teller,
        'unknown': UserRole.unknown,
      };

      roles.forEach((roleString, expectedRole) {
        final json = {
          'id': 1,
          'fullName': 'Test User',
          'email': '<EMAIL>',
          'role': roleString,
          'phoneNumber': null
        };

        final user = User.fromJson(json);
        expect(user.role, expectedRole, reason: 'Role $roleString should map to $expectedRole');
      });
    });
  });
}
