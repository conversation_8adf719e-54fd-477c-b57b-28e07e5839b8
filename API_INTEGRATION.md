# API Integration with FastAPI Backend

## Overview
The Flutter app has been successfully configured to connect to your FastAPI backend deployed on Railway.

## Configuration Details

### Backend URL
- **Production URL**: `https://lc-groupscop.up.railway.app`
- **Base URL configured in**: `lib/config/app_config.dart`
- **API Version**: `/api/v1`

### API Service Setup

#### 1. API Client (`lib/services/api_client.dart`)
- Centralized Dio configuration
- Automatic error handling
- Request/response logging (debug mode only)
- Authentication token management
- Timeout configurations (30 seconds)

#### 2. API Service (`lib/services/api_service.dart`)
- Generated using Retrofit
- Type-safe API endpoints
- Automatic JSON serialization/deserialization

#### 3. Configuration (`lib/config/app_config.dart`)
- Centralized app configuration
- Environment-specific settings
- Easy to modify for different environments

## Available API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login (OAuth2 form-encoded with username/password)
- `POST /api/v1/auth/login/email` - User login with email/password (JSON)
- `POST /api/v1/auth/logout` - User logout

### Loan Applications
- `GET /api/v1/loans/` - Get all loan applications
- `GET /api/v1/loans/{id}` - Get specific loan application
- `POST /api/v1/loans/` - Create new loan application
- `PUT /api/v1/loans/{id}` - Update loan application

## Usage Examples

### Login with Username
```dart
import 'package:loan_pawn_flutter/services/api_client.dart';

// Login user with username
try {
  final loginResponse = await ApiClient.apiService.login('username', 'password123');

  // Store the authentication token
  ApiClient.setAuthToken(loginResponse.token.accessToken);

  print('Login successful: ${loginResponse.user.username}');
  print('Token expires in: ${loginResponse.token.expiresIn} seconds');
} catch (e) {
  print('Login failed: $e');
}
```

### Login with Email (Alternative)
```dart
// Login user with email
try {
  final credentials = {
    'email': '<EMAIL>',
    'password': 'password123'
  };
  final loginResponse = await ApiClient.apiService.loginWithEmail(credentials);

  // Store the authentication token
  ApiClient.setAuthToken(loginResponse.token.accessToken);

  print('Login successful: ${loginResponse.user.email}');
} catch (e) {
  print('Login failed: $e');
}
```

### Create Loan Application
```dart
// Create new loan application
try {
  final application = LoanApplication(
    applicantName: 'John Doe',
    nidNumber: '123456789',
    // ... other required fields
  );
  final result = await ApiClient.apiService.createApplication(application);
  print('Application created: ${result.id}');
} catch (e) {
  print('Failed to create application: $e');
}
```

### Authentication Token Management
```dart
// Set auth token after login
ApiClient.setAuthToken('your-jwt-token');

// Clear auth token on logout
ApiClient.clearAuthToken();
```

## Error Handling

The API client includes comprehensive error handling:

- **Network errors**: Connection timeouts, no internet
- **HTTP errors**: 401 (Unauthorized), 403 (Forbidden), 404 (Not Found), 500 (Server Error)
- **Validation errors**: Invalid request data
- **Authentication errors**: Invalid credentials, expired tokens

## Testing

Run API tests to verify configuration:
```bash
flutter test test/api_test.dart
```

## Security Features

1. **HTTPS**: All API calls use secure HTTPS protocol
2. **Token-based authentication**: JWT tokens for secure API access
3. **Request validation**: Type-safe requests prevent malformed data
4. **Error sanitization**: Sensitive information not exposed in error messages

## Environment Configuration

To switch between different environments (development, staging, production):

1. Update `AppConfig.apiBaseUrl` in `lib/config/app_config.dart`
2. Set `AppConfig.isProduction` accordingly
3. Rebuild the app

## Next Steps

1. **Test the login endpoint** with actual credentials from your FastAPI backend
2. **Verify API endpoint compatibility** between Flutter models and FastAPI schemas
3. **Implement proper error handling** in UI components
4. **Add authentication state management** for persistent login
5. **Test all CRUD operations** for loan applications

## Troubleshooting

### Common Issues

1. **Connection timeout**: Check if the Railway URL is accessible
2. **404 errors**: Verify endpoint paths match your FastAPI routes
3. **CORS errors**: Ensure your FastAPI backend allows requests from your app
4. **Authentication errors**: Verify JWT token format and validation

### Debug Mode

Enable detailed logging by setting `AppConfig.enableApiLogging = true` in debug builds.

## Files Modified/Created

- ✅ `lib/services/api_service.dart` - Updated base URL
- ✅ `lib/services/api_client.dart` - New centralized API client
- ✅ `lib/config/app_config.dart` - New configuration file
- ✅ `lib/screens/auth/login_screen.dart` - Updated to use new API client
- ✅ `test/api_test.dart` - API configuration tests
- ✅ `API_INTEGRATION.md` - This documentation

The Flutter app is now ready to communicate with your FastAPI backend on Railway! 🚀
