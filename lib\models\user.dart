// TODO: Implement User model as per project requirements
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@HiveType(typeId: 5) // Using a new typeId
enum UserRole {
  @HiveField(0)
  @JsonValue('po')
  pawnOfficer,

  @HiveField(1)
  @JsonValue('cpo')
  chiefPawnOfficer,

  @HiveField(2)
  @JsonValue('teller')
  teller,

  @HiveField(3)
  @JsonValue('unknown')
  unknown,
}

@HiveType(typeId: 6) // Using a new typeId
@JsonSerializable(createFactory: false)
class User {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String fullName;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final UserRole role;

  @HiveField(4)
  final String? phoneNumber;

  const User({
    required this.id,
    required this.fullName,
    required this.email,
    this.role = UserRole.unknown,
    this.phoneNumber,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']
          .toString(), // Convert to string to handle both int and string IDs
      fullName: json['fullName'] as String,
      email: json['email'] as String,
      role: _parseUserRole(json['role']),
      phoneNumber: json['phoneNumber'] as String?,
    );
  }

  static UserRole _parseUserRole(dynamic roleValue) {
    if (roleValue == null) return UserRole.unknown;

    // Handle string values
    if (roleValue is String) {
      switch (roleValue.toLowerCase()) {
        case 'po':
          return UserRole.pawnOfficer;
        case 'cpo':
          return UserRole.chiefPawnOfficer;
        case 'teller':
          return UserRole.teller;
        default:
          return UserRole.unknown;
      }
    }

    return UserRole.unknown;
  }

  Map<String, dynamic> toJson() => _$UserToJson(this);
}
