import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:loan_pawn_flutter/services/api_client.dart';
import 'package:loan_pawn_flutter/config/app_config.dart';

void main() {
  group('API Configuration Tests', () {
    test('API Client should be configured with correct base URL', () {
      final dio = ApiClient.dio;
      expect(dio.options.baseUrl, equals(AppConfig.apiBaseUrl));
      expect(
          dio.options.baseUrl, equals('https://lc-groupscop.up.railway.app'));
    });

    test('API Client should have correct timeout configurations', () {
      final dio = ApiClient.dio;
      expect(dio.options.connectTimeout, equals(AppConfig.apiConnectTimeout));
      expect(dio.options.receiveTimeout, equals(AppConfig.apiReceiveTimeout));
      expect(dio.options.sendTimeout, equals(AppConfig.apiSendTimeout));
    });

    test('API Client should have correct headers', () {
      final dio = ApiClient.dio;
      expect(dio.options.headers['Content-Type'], equals('application/json'));
      expect(dio.options.headers['Accept'], equals('application/json'));
    });

    test('API Service should be accessible', () {
      final apiService = ApiClient.apiService;
      expect(apiService, isNotNull);
    });
  });

  group('API Connection Tests', () {
    test('Should be able to make a basic request to the API', () async {
      try {
        final dio = ApiClient.dio;

        // Test a simple GET request to check if the API is reachable
        // This might fail if the API is not running, but it will test the configuration
        final response = await dio.get('/health').timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            throw TimeoutException(
                'API connection timeout', const Duration(seconds: 10));
          },
        );

        // If we get here, the API is reachable
        expect(response.statusCode, lessThan(500));
      } on DioException catch (e) {
        // Expected if API is not running or endpoint doesn't exist
        // But we can still verify the request was properly formatted
        expect(e.requestOptions.baseUrl, equals(AppConfig.apiBaseUrl));
        print('API connection test: ${e.message}');
      } on TimeoutException catch (e) {
        print('API timeout test: ${e.message}');
        // This is also acceptable for testing purposes
      }
    });
  });
}

class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}
