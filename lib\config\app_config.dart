/// Application configuration constants
class AppConfig {
  // API Configuration
  static const String apiBaseUrl = 'https://lc-groupscop.up.railway.app';

  // Timeout configurations
  static const Duration apiConnectTimeout = Duration(seconds: 30);
  static const Duration apiReceiveTimeout = Duration(seconds: 30);
  static const Duration apiSendTimeout = Duration(seconds: 30);

  // App Information
  static const String appName = 'LC Loan App';
  static const String appVersion = '1.0.0';

  // Environment
  static const bool isProduction = true;
  static const bool enableApiLogging = !isProduction;

  // Storage Keys
  static const String userStorageKey = 'user';
  static const String tokenStorageKey = 'auth_token';

  // Google Cloud Configuration
  static const String googleCloudProjectId = 'lc-le-flutter';
  static const String googleCloudCredentialsPath =
      'assets/google-cloud-credentials.json';
}
